using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using Excalibur.Models;
using System.Timers;
using Excalibur.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class SimulationViewModel : BaseViewModel, IDisposable
{
    private ObservableCollection<ContractTransaction> _simulationTransactions;
    private int _nextTransactionId = 1;
    private System.Timers.Timer _expirationTimer;
    private decimal _currentSpot;
    private readonly object _lock = new object();

    // Sistema de contagem Win/Loss
    private int _simulationWins = 0;
    private int _simulationLosses = 0;
    private int _targetWins = 2;
    private int _targetLosses = 3;
    private int _sampleSize = 5;

    // Event para trigger de compra real
    public event EventHandler? TriggerRealPurchase;

    public SimulationViewModel()
    {
        _simulationTransactions = new ObservableCollection<ContractTransaction>();
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] SimulationViewModel criado - SimulationTransactions inicializada com Count={_simulationTransactions.Count}");
        
        // Timer para verificar expiração de contratos simulados
        _expirationTimer = new System.Timers.Timer(1000);
        _expirationTimer.Elapsed += CheckSimulationExpirations;
        _expirationTimer.AutoReset = true;
        _expirationTimer.Start();
    }

    public ObservableCollection<ContractTransaction> SimulationTransactions
    {
        get => _simulationTransactions;
        set
        {
            _simulationTransactions = value;
            OnPropertyChanged();
        }
    }

    // Propriedades para o sistema Win/Loss
    public int SimulationWins 
    { 
        get => _simulationWins; 
        private set 
        { 
            _simulationWins = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(SimulationStatus));
        } 
    }

    public int SimulationLosses 
    { 
        get => _simulationLosses; 
        private set 
        { 
            _simulationLosses = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(SimulationStatus));
        } 
    }

    public string SimulationStatus => $"de {_sampleSize} (Meta: {_targetWins} W / {_targetLosses} L)";

    // Propriedade para contar total de entradas na simulação
    public int TotalSimulationEntries => _simulationTransactions?.Count ?? 0;

    public void AddTransaction(ContractTransaction transaction)
    {
        // Garantir que a adição seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => AddTransaction(transaction));
            return;
        }

        lock (_lock)
        {
            // Gerar ID único se não fornecido
            if (transaction.Id == 0)
            {
                transaction.Id = _nextTransactionId++;
            }

            // Garantir que seja marcado como ativo para simulação
            transaction.IsActive = true; // Marcar como ativo para simulação
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ======= ADICIONANDO NOVA SIMULAÇÃO =======");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ANTES: SimulationTransactions.Count = {_simulationTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Nova transação:");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - ID: {transaction.Id}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Type: {transaction.Type}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - RefId: {transaction.RefId}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - BuyTime: {transaction.BuyTime:HH:mm:ss.fff}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Stake: {transaction.Stake}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Payout: {transaction.Payout}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Duration: {transaction.Duration} {transaction.DurationType}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - IsActive: {transaction.IsActive}");
            
            var expectedDuration = transaction.GetExpirationTimeInSeconds();
            var expectedExpiration = transaction.BuyTime.AddSeconds(expectedDuration);
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Duração calculada: {expectedDuration}s");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION]   - Expiração esperada: {expectedExpiration:HH:mm:ss.fff}");
            
            try
            {
                _simulationTransactions.Add(transaction);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ERRO ao adicionar transação: {ex.Message}");
                throw;
            }
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] DEPOIS: SimulationTransactions.Count = {_simulationTransactions.Count}");
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ======= FIM DA ADIÇÃO =======");
        }
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(SimulationTransactions));
        OnPropertyChanged(nameof(TotalSimulationEntries));
    }

    public void UpdateTransaction(string refId, decimal endSpot, decimal totalProfitLoss, bool isClosed = false)
    {
        lock (_lock)
        {
            var transaction = _simulationTransactions.FirstOrDefault(t => t.RefId == refId);
            if (transaction != null)
            {
                transaction.EndSpot = endSpot;
                transaction.TotalProfitLoss = totalProfitLoss;
                
                if (isClosed)
                {
                    transaction.SellTime = DateTime.Now;
                    transaction.IsActive = false;
                    
                    // Avaliar resultado e atualizar contadores
                    EvaluateSimulationResult(transaction);
                }
            }
        }
    }

    public void UpdateCurrentSpot(decimal currentSpot)
    {
        _currentSpot = currentSpot;
        
        lock (_lock)
        {
            // Atualizar contratos simulados ativos
            foreach (var transaction in _simulationTransactions.Where(t => t.IsActive))
            {
                transaction.EndSpot = currentSpot;
                
                // CORREÇÃO: Para contratos binários, calcular P/L baseado em win/loss, não na diferença de preço
                if (transaction.StartSpot > 0)
                {
                    // Verificar se o contrato está atualmente ganhando
                    bool isCurrentlyWinning = transaction.IsCurrentlyWinning(currentSpot);

                    // Para contratos binários, o P/L é fixo baseado em win/loss
                    if (isCurrentlyWinning)
                    {
                        // Se está ganhando: Payout - Stake
                        transaction.TotalProfitLoss = transaction.Payout - transaction.Stake;
                    }
                    else
                    {
                        // Se está perdendo: -Stake
                        transaction.TotalProfitLoss = -transaction.Stake;
                    }
                }
            }
        }
    }

    public void UpdateSampleConfiguration(int amostra, int wins, int losses)
    {
        // Garantir que a amostra seja sempre a soma de wins + losses
        var calculatedSample = wins + losses;
        if (amostra != calculatedSample)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ CORRIGINDO AMOSTRA: fornecida={amostra}, calculada={calculatedSample} (wins={wins} + losses={losses})");
            amostra = calculatedSample;
        }
        
        _sampleSize = amostra;
        _targetWins = wins;
        _targetLosses = losses;
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ======= CONFIGURAÇÃO ATUALIZADA =======");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Amostra: {_sampleSize}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Target Wins: {_targetWins}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Target Losses: {_targetLosses}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Estado atual: Wins={SimulationWins}, Losses={SimulationLosses}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ======= FIM DA CONFIGURAÇÃO =======");
        OnPropertyChanged(nameof(SimulationStatus));
    }

    private void CheckSimulationExpirations(object sender, ElapsedEventArgs e)
    {
        var now = DateTime.Now;
        
        lock (_lock)
        {
            // Debug: verificar se há transações ativas
            var activeTransactions = _simulationTransactions.Where(t => t.IsActive).ToList();
            var totalTransactions = _simulationTransactions.Count;
            
            // Remover logs verbosos para reduzir processamento
            // Log só quando há mudanças significativas (expirações)
            
            var expiredTransactions = _simulationTransactions
                .Where(t => t.IsActive)
                .Where(t =>
                {
                    var timeElapsed = (now - t.BuyTime).TotalSeconds;
                    var expirationTime = t.GetExpirationTimeInSeconds();
                    var isExpired = timeElapsed >= expirationTime;
                    
                    if (isExpired)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⏰ CONTRATO EXPIRANDO: BuyTime={t.BuyTime:HH:mm:ss}, " +
                            $"Now={now:HH:mm:ss}, Elapsed={timeElapsed:F1}s, Required={expirationTime}s");
                    }
                    
                    return isExpired;
                })
                .ToList();

            foreach (var transaction in expiredTransactions)
            {
                transaction.EndSpot = _currentSpot;
                
                // Calcular SellTime exato baseado no BuyTime + duração para precisão
                var exactSellTime = transaction.BuyTime.AddSeconds(transaction.GetExpirationTimeInSeconds());
                transaction.SellTime = exactSellTime;
                transaction.IsActive = false;
                
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] SellTime calculado: BuyTime={transaction.BuyTime:HH:mm:ss} + {transaction.GetExpirationTimeInSeconds()}s = {exactSellTime:HH:mm:ss}");
                
                // Calcular resultado final usando a mesma lógica do PurchaseViewModel
                CalculateFinalPayout(transaction);
                
                // Avaliar resultado
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🎯 Avaliando resultado da transação expirada: ID={transaction.Id}");
                EvaluateSimulationResult(transaction);
                
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] Contrato simulado expirado: ID={transaction.Id}, Result={transaction.TotalProfitLoss}");
            }
        }
    }

    private void CalculateFinalPayout(ContractTransaction transaction)
    {
        // Usar a mesma lógica do PurchaseViewModel para calcular payout final
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ====== CALCULANDO PAYOUT FINAL ======");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] RefId: {transaction.RefId}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Tipo: {transaction.Type}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] StartSpot: {transaction.StartSpot}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] EndSpot (CurrentSpot): {_currentSpot}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Payout: {transaction.Payout}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Stake: {transaction.Stake}");
        
        // Primeiro, verificar se está ganhando usando IsCurrentlyWinning
        bool isWinning = transaction.IsCurrentlyWinning(_currentSpot);
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] IsCurrentlyWinning retornou: {isWinning}");
        
        // Calcular variação de preço para referência
        decimal priceVariation = _currentSpot - transaction.StartSpot;
        decimal maxPayout = transaction.Payout - transaction.Stake;
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Variação de preço: {priceVariation:F5}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Max Payout (se ganhar): {maxPayout:F2}");
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] Loss (se perder): -{transaction.Stake:F2}");
        
        if (isWinning)
        {
            // Ganhou: usar payout máximo
            transaction.TotalProfitLoss = maxPayout;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ✅ CONTRATO GANHOU - TotalProfitLoss definido para: {maxPayout:F2}");
        }
        else
        {
            // Perdeu: usar perda total (stake)
            transaction.TotalProfitLoss = -transaction.Stake;
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] ❌ CONTRATO PERDEU - TotalProfitLoss definido para: -{transaction.Stake:F2}");
        }
        
        System.Diagnostics.Debug.WriteLine($"[SIMULATION] ====== FIM DO CÁLCULO ======");
    }

    private void EvaluateSimulationResult(ContractTransaction transaction)
    {
        bool isWin = transaction.TotalProfitLoss > 0;
        
        // Usar a nova função ProcessSimulationResult para manter consistência
        ProcessSimulationResult(transaction, isWin);
    }

    public void ClearTransactions()
    {
        // Garantir que a limpeza seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => ClearTransactions());
            return;
        }

        lock (_lock)
        {
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Limpando {_simulationTransactions.Count} transações da simulação");
            _simulationTransactions.Clear();
            
            // Reset dos contadores
            SimulationWins = 0;
            SimulationLosses = 0;
            
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] Simulação limpa - Wins: {SimulationWins}, Losses: {SimulationLosses}");
        }
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(SimulationTransactions));
        OnPropertyChanged(nameof(TotalSimulationEntries));
    }

    public void Dispose()
    {
        _expirationTimer?.Stop();
        _expirationTimer?.Dispose();
        
        lock (_lock)
        {
            _simulationTransactions.Clear();
        }
        
        GC.SuppressFinalize(this);
    }

    // Nova função para verificar critérios de simulação
    public Task<bool> CheckSimulationCriteriaAsync()
    {
        return Task.Run(() =>
        {
            lock (_lock)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔍 Verificando critérios de simulação...");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔍 Wins: {SimulationWins}/{_targetWins}, Losses: {SimulationLosses}/{_targetLosses}");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔍 Total transações: {_simulationTransactions.Count}/{_sampleSize}");
                
                // Verificar se há transações suficientes na amostra
                if (_simulationTransactions.Count < _sampleSize)
                {
                    System.Diagnostics.Debug.WriteLine($"[SIMULATION] ❌ Amostra insuficiente: {_simulationTransactions.Count}/{_sampleSize}");
                    return false;
                }

                // Verificar se critérios de Win/Loss são atendidos (match exato)
                bool criteriaAreMet = SimulationWins == _targetWins && SimulationLosses == _targetLosses;
                
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] {(criteriaAreMet ? "✅" : "❌")} Critérios: {(criteriaAreMet ? "ATENDIDOS" : "NÃO ATENDIDOS")}");
                
                return criteriaAreMet;
            }
        });
    }

    private void ProcessSimulationResult(ContractTransaction transaction, bool isWin)
    {
        lock (_lock)
        {
            var result = isWin ? "WIN" : "LOSS";
            System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🎯 Processando resultado: {result} para {transaction.RefId}");
            
            if (isWin)
            {
                SimulationWins++;
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ✅ VITÓRIA! Total wins: {SimulationWins}/{_targetWins}");
            }
            else
            {
                SimulationLosses++;
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ❌ PERDA! Total losses: {SimulationLosses}/{_targetLosses}");
            }

            System.Diagnostics.Debug.WriteLine($"[SIMULATION] 📊 Estado atual: Wins={SimulationWins}/{_targetWins}, Losses={SimulationLosses}/{_targetLosses}, Sample={_simulationTransactions.Count}/{_sampleSize}");
            
            // Verificar se critérios foram atendidos (match exato)
            bool exactMatch = SimulationWins == _targetWins && SimulationLosses == _targetLosses;
            bool sampleComplete = SimulationWins + SimulationLosses >= _sampleSize;
            
            if (exactMatch)
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🎯 CRITÉRIOS ATENDIDOS! Disparando TriggerRealPurchase");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🎯 Wins: {SimulationWins}/{_targetWins}, Losses: {SimulationLosses}/{_targetLosses}, Sample: {_simulationTransactions.Count}/{_sampleSize}");
                
                // Disparar evento para compra real
                TriggerRealPurchase?.Invoke(this, EventArgs.Empty);
                
                // Reset counters para permitir novo ciclo (importante para modo "Manter")
                SimulationWins = 0;
                SimulationLosses = 0;
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔄 Contadores resetados para novo ciclo (Wins=0, Losses=0)");
            }
            else if (sampleComplete)
            {
                // ⚠️ FALHA: Amostra completa mas não atingiu a meta exata
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ Amostra completada sem atingir meta exata. Resetando contadores...");
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] ⚠️ Resultado obtido: {SimulationWins}W + {SimulationLosses}L (esperado: {_targetWins}W + {_targetLosses}L)");
                SimulationWins = 0;
                SimulationLosses = 0;
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 🔄 Contadores resetados após amostra incompleta");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[SIMULATION] 📊 Critérios ainda não atendidos. Continuando simulação...");
            }
        }
    }
} 